# <PERSON>i - visualization of differential mutation analysis results

library(ggplot2)
library(dplyr)
library(optparse)

# Parse command line arguments
option_list <- list(
  make_option(c("--stats_file"), type="character", default=NULL,
              help="Input statistics file", metavar="character"),
  make_option(c("--volcano_plot"), type="character", default=NULL,
              help="Output volcano plot PDF", metavar="character"),
  make_option(c("--summary_plot"), type="character", default=NULL,
              help="Output summary plot PDF", metavar="character")
)

opt_parser <- OptionParser(option_list=option_list)
opt <- parse_args(opt_parser)

print("001c-visualize_differential_mutations.R")

# Load data
cat("Loading statistics file:", opt$stats_file, "\n")
df <- read.table(opt$stats_file, sep="\t", header=TRUE, stringsAsFactors=FALSE)

cat("Loaded", nrow(df), "genes\n")

# Add significance categories
df$significance <- "Not significant"
df$significance[df$pvalue_adjusted < 0.05 & df$log2_fold_change > 0] <- "Late-enriched"
df$significance[df$pvalue_adjusted < 0.05 & df$log2_fold_change < 0] <- "Early-enriched"

# Create volcano plot
cat("Creating volcano plot...\n")

volcano_plot <- ggplot(df, aes(x=log2_fold_change, y=-log10(pvalue_adjusted), color=significance)) +
  geom_point(alpha=0.6, size=1.5) +
  scale_color_manual(values=c("Early-enriched"="blue", "Late-enriched"="red", "Not significant"="gray")) +
  geom_hline(yintercept=-log10(0.05), linetype="dashed", color="black") +
  geom_vline(xintercept=0, linetype="dashed", color="black") +
  labs(
    title="Differential Mutation Analysis: Early vs Late Metastasis",
    x="Log2 Fold Change (Late vs Early)",
    y="-Log10 Adjusted P-value",
    color="Significance"
  ) +
  theme_bw() +
  theme(
    plot.title = element_text(hjust = 0.5, size=14),
    legend.position = "bottom"
  )

# Add gene labels for top significant genes
top_genes <- df %>%
  filter(pvalue_adjusted < 0.05) %>%
  arrange(pvalue_adjusted) %>%
  head(10)

if(nrow(top_genes) > 0) {
  volcano_plot <- volcano_plot +
    ggrepel::geom_text_repel(
      data=top_genes,
      aes(label=gene),
      size=3,
      max.overlaps=10
    )
}

# Save volcano plot
pdf(opt$volcano_plot, width=10, height=8)
print(volcano_plot)
dev.off()

# Create summary plot
cat("Creating summary plot...\n")

# Summary statistics
sig_genes <- df %>% filter(pvalue_adjusted < 0.05)
summary_stats <- data.frame(
  Category = c("Total genes tested", "Significant genes", "Late-enriched", "Early-enriched"),
  Count = c(
    nrow(df),
    nrow(sig_genes),
    nrow(sig_genes %>% filter(log2_fold_change > 0)),
    nrow(sig_genes %>% filter(log2_fold_change < 0))
  )
)

summary_plot <- ggplot(summary_stats, aes(x=Category, y=Count, fill=Category)) +
  geom_bar(stat="identity") +
  geom_text(aes(label=Count), vjust=-0.3, size=4) +
  scale_fill_brewer(palette="Set3") +
  labs(
    title="Summary of Differential Mutation Analysis",
    x="Category",
    y="Number of Genes"
  ) +
  theme_bw() +
  theme(
    plot.title = element_text(hjust = 0.5, size=14),
    legend.position = "none",
    axis.text.x = element_text(angle=45, hjust=1)
  )

# Save summary plot
pdf(opt$summary_plot, width=8, height=6)
print(summary_plot)
dev.off()

# Print summary to console
cat("\n=== SUMMARY ===\n")
cat("Total genes tested:", nrow(df), "\n")
cat("Significant genes (FDR < 0.05):", nrow(sig_genes), "\n")
if(nrow(sig_genes) > 0) {
  cat("Late-enriched genes:", nrow(sig_genes %>% filter(log2_fold_change > 0)), "\n")
  cat("Early-enriched genes:", nrow(sig_genes %>% filter(log2_fold_change < 0)), "\n")
  
  cat("\nTop 5 most significant genes:\n")
  top5 <- sig_genes %>% arrange(pvalue_adjusted) %>% head(5)
  for(i in 1:min(5, nrow(top5))) {
    cat(sprintf("%s: p_adj=%.2e, log2FC=%.2f (%s)\n", 
                top5$gene[i], top5$pvalue_adjusted[i], top5$log2_fold_change[i], top5$direction[i]))
  }
}

cat("Plots saved to:", opt$volcano_plot, "and", opt$summary_plot, "\n")
cat("001c-visualize_differential_mutations.R COMPLETE\n")
