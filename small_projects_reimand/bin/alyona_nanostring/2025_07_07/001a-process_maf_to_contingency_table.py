# Alec Bahcheli - differential mutation analysis between early and late groups

import sys, getopt, time, os
import pandas as pd 
import numpy as np

help_message = '''
Process MAF files to create contingency table for differential mutation analysis
Usage: python 001a-process_maf_to_contingency_table.py --early_maf_file <file> --late_maf_file <file> --contingency_file <output>
'''

def process_maf_file(maf_file, group_name):
    """
    Process a single MAF file and return mutation counts per gene
    """
    print(f"Processing {group_name} MAF file: {maf_file}")

    # Load MAF file
    df = pd.read_csv(maf_file, sep="\t", low_memory=False)

    print(f"Loaded {len(df)} mutations from {group_name}")

    # Clean up the data
    # Use SYMBOL column instead of Hugo_Symbol, and remove rows where SYMB<PERSON> is NaN, empty, or invalid
    df = df[df['SYMBOL'].notna()]
    df = df[~df['SYMBOL'].isin(['', 'SYMBOL'])]  # Remove empty strings and header artifacts

    print(f"After filtering unknown genes: {len(df)} mutations")

    if len(df) == 0:
        print(f"Warning: No valid gene names found in {group_name} MAF file")
        return pd.DataFrame(columns=['gene', f'{group_name}_mutated', f'{group_name}_total'])

    # Check unique gene names
    unique_genes = df['SYMBOL'].unique()
    print(f"Found {len(unique_genes)} unique genes")
    if len(unique_genes) <= 10:
        print(f"Gene names: {list(unique_genes)}")

    # Check unique samples
    unique_samples = df['Tumor_Sample_Barcode'].unique()
    print(f"Found {len(unique_samples)} unique samples")

    # Count mutations per gene per sample
    gene_sample_counts = df.groupby(['SYMBOL', 'Tumor_Sample_Barcode']).size().reset_index(name='mutation_count')

    # Convert to binary: 1 if gene is mutated in sample, 0 if not
    gene_sample_binary = gene_sample_counts.copy()
    gene_sample_binary['mutation_count'] = (gene_sample_binary['mutation_count'] > 0).astype(int)

    # Get unique samples and genes
    samples = gene_sample_binary['Tumor_Sample_Barcode'].unique()
    genes = gene_sample_binary['SYMBOL'].unique()

    print(f"{group_name}: {len(samples)} samples, {len(genes)} genes with mutations")

    # Create a pivot table: genes as rows, samples as columns
    pivot_df = gene_sample_binary.pivot(index='SYMBOL', columns='Tumor_Sample_Barcode', values='mutation_count')
    pivot_df = pivot_df.fillna(0).astype(int)

    # Sum across samples to get total number of samples with mutations per gene
    gene_mutation_counts = pivot_df.sum(axis=1)

    # Get total number of samples
    total_samples = len(samples)

    # Create result dataframe
    result_df = pd.DataFrame({
        'gene': gene_mutation_counts.index,
        f'{group_name}_mutated': gene_mutation_counts.values,
        f'{group_name}_total': total_samples
    })

    return result_df

def create_contingency_table(early_maf_file, late_maf_file):
    """
    Process both MAF files and create contingency table
    """
    # Process early group
    early_df = process_maf_file(early_maf_file, 'early')
    
    # Process late group  
    late_df = process_maf_file(late_maf_file, 'late')
    
    # Merge the dataframes on gene
    merged_df = pd.merge(early_df, late_df, on='gene', how='outer')
    
    # Fill NaN values with 0 for genes not present in one of the groups
    merged_df = merged_df.fillna(0)
    
    # Convert to integers
    merged_df['early_mutated'] = merged_df['early_mutated'].astype(int)
    merged_df['late_mutated'] = merged_df['late_mutated'].astype(int)
    merged_df['early_total'] = merged_df['early_total'].astype(int)
    merged_df['late_total'] = merged_df['late_total'].astype(int)
    
    # For genes not present in one group, set the total to the maximum total from the other group
    # This assumes similar sample sizes between groups
    max_early_total = merged_df['early_total'].max()
    max_late_total = merged_df['late_total'].max()
    
    merged_df.loc[merged_df['early_total'] == 0, 'early_total'] = max_early_total
    merged_df.loc[merged_df['late_total'] == 0, 'late_total'] = max_late_total
    
    # Calculate non-mutated counts
    merged_df['early_not_mutated'] = merged_df['early_total'] - merged_df['early_mutated']
    merged_df['late_not_mutated'] = merged_df['late_total'] - merged_df['late_mutated']
    
    print(f"Final contingency table: {len(merged_df)} genes")
    print(f"Early group: {max_early_total} samples")
    print(f"Late group: {max_late_total} samples")
    
    return merged_df

def main():
    print('001a-process_maf_to_contingency_table.py')
    t1 = time.time()

    # Create contingency table from MAF files
    contingency_df = create_contingency_table(early_maf_file, late_maf_file)

    # Save the contingency table
    contingency_df.to_csv(contingency_file, sep="\t", index=False)
    
    print(f"Saved contingency table to: {contingency_file}")
    print(f"Processing completed in {round(time.time() - t1, 2)} seconds")
    print('001a-process_maf_to_contingency_table.py COMPLETE')

if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["early_maf_file=", "late_maf_file=", "contingency_file="])
    except getopt.GetoptError:
        print(help_message)
        print(sys.argv)
        sys.exit(2)
    
    for opt, arg in opts:
        if opt in ("--early_maf_file"):
            early_maf_file = str(arg)
        elif opt in ("--late_maf_file"):
            late_maf_file = str(arg)
        elif opt in ("--contingency_file"):
            contingency_file = str(arg)
    
    main()
