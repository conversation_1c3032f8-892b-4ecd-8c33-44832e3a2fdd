# Alec <PERSON>cheli - differential mutation analysis between early and late metastasis groups

###################################
# Differential mutation analysis between early and late groups
###################################

# Process MAF files to create contingency table
rule process_maf_to_contingency_table:
    input:
        early_maf_file = DATA_DIR + "/raw_data/earlyMetMerged.maf",
        late_maf_file = DATA_DIR + "/raw_data/lateMetMerged.maf",
        script = BIN_DIR + "/001a-process_maf_to_contingency_table.py"

    output:
        contingency_file = RES_DIR + "/mutation_analysis/maf_contingency_table.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --early_maf_file {input.early_maf_file} --late_maf_file {input.late_maf_file} --contingency_file {output.contingency_file}"

# Perform statistical testing on contingency table
rule maf_contingency_table_stats:
    input:
        contingency_file = RES_DIR + "/mutation_analysis/maf_contingency_table.tsv",
        script = BIN_DIR + "/001b-maf_contingency_table_stats.py"

    output:
        stats_file = RES_DIR + "/mutation_analysis/differential_mutation_stats.tsv"

    resources:
        threads = 4,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '8G'

    shell:
        "{PYTHON} {input.script} --contingency_file {input.contingency_file} --stats_file {output.stats_file} --threads {resources.threads}"

# Optional: Create visualization of results
rule visualize_differential_mutations:
    input:
        stats_file = RES_DIR + "/mutation_analysis/differential_mutation_stats.tsv",
        script = BIN_DIR + "/001c-visualize_differential_mutations.R"

    output:
        volcano_plot = RES_DIR + "/figures/differential_mutations_volcano_plot.pdf",
        summary_plot = RES_DIR + "/figures/differential_mutations_summary.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '16G'

    shell:
        "{RSCRIPT} {input.script} --stats_file {input.stats_file} --volcano_plot {output.volcano_plot} --summary_plot {output.summary_plot}"

