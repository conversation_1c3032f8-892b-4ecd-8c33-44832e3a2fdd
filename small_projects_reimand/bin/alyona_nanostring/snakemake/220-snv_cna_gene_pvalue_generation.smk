# Alec <PERSON>cheli - <EMAIL>


###################################
# consistently differentially copy numbered CNAs primary-recurrent 
# nanopore SVs included
###################################

# CNA analysis continuous 
rule cna_gene_continuous_prep:
    input:
        combined_pcg_cna_file = RES_DIR + "/analysis_sarek/gene_impacts/all_combined_pcg_cnas.bed",

        script = BIN_DIR + "/sarek_cnas/010a-cna_gene_continuous_prep.py"

    output:
        cna_continuous_file = RES_DIR + "/analysis_sarek/gene_impacts/cna_gene_continuous_prep.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_pcg_cna_file {input.combined_pcg_cna_file} --cna_continuous_file {output.cna_continuous_file}"

rule cna_gene_continuous_stats:
    input:
        cna_continuous_file = RES_DIR + "/analysis_sarek/gene_impacts/cna_gene_continuous_prep.tsv",

        script = BIN_DIR + "/sarek_cnas/010b-cna_gene_continuous_stats.py"

    output:
        cna_continuous_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_gene_continuous_stats.tsv"
        
    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --cna_continuous_file {input.cna_continuous_file} --cna_continuous_stats_file {output.cna_continuous_stats_file}"



# repeat for SVs from nanopore
rule nanopore_sv_gene_stats:
    input:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/sv_protein_coding_genes_intersected.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/010a-nanopore_sv_gene_stats.py"

    output:
        nanopore_sv_gene_stats_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/nanopore_sv_gene_stats.tsv"
        
    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --nanopore_sv_gene_stats_file {output.nanopore_sv_gene_stats_file} --threads {resources.threads}"


# merge nanopore and WGS CNAs / SV p-values
rule combine_nanopore_wgs_cnas_svs_pvalues:
    input:
        nanopore_sv_gene_stats_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/nanopore_sv_gene_stats.tsv",
        cna_continuous_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_gene_continuous_stats.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/010c-combine_nanopore_wgs_cnas_svs_pvalues.py"

    output:
        nanopore_wgs_cnas_svs_merged_file = RES_DIR + "/analysis_nanopore/svs/nanopore_wgs_cnas_svs_combined.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --nanopore_sv_gene_stats_file {input.nanopore_sv_gene_stats_file} --cna_continuous_stats_file {input.cna_continuous_stats_file} --nanopore_wgs_cnas_svs_merged_file {output.nanopore_wgs_cnas_svs_merged_file}"



# # run stats per gene for SVs from nanopore
# rule nanopore_sv_rates_per_gene:
#     input:
#         intersected_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/sv_protein_coding_genes_intersected.tsv",
#         filtered_svs_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/processed_nanopore_svs.bed",

#         pcg_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
#         genome_size_file = REF_DATA_DIR + "/genome_size_accessible_blacklist/genome_size.tsv",

#         script = BIN_DIR + "/nanopore_svs_cnas/011a-nanopore_sv_rates_per_gene.py"

#     output:
#         svs_per_gene_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/nanopore_sv_rates_per_gene.tsv"
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --filtered_svs_file {input.filtered_svs_file} --pcg_bed_file {input.pcg_bed_file} --genome_size_file {input.genome_size_file} --svs_per_gene_file {output.svs_per_gene_file}"

# rule nanopore_sv_gene_stats_nb_model_anova:
#     input:
#         svs_per_gene_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/nanopore_sv_rates_per_gene.tsv",

#         script = BIN_DIR + "/nanopore_svs_cnas/011b-nanopore_sv_gene_stats_nb_model_anova.py"

#     output:
#         nanopore_sv_gene_stats_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/nanopore_sv_gene_stats.tsv"
        
#     resources:
#         threads = 20,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '3G'
        
#     shell:
#         "{PYTHON} {input.script} --svs_per_gene_file {input.svs_per_gene_file} --nanopore_sv_gene_stats_file {output.nanopore_sv_gene_stats_file} --threads {resources.threads}"


# rule nanopore_sv_gene_stats_nb_model_anova_R:
#     input:
#         svs_per_gene_file = RES_DIR + "/analysis_nanopore/svs/nanopore_sv_rates_per_gene.tsv",

#         script = BIN_DIR + "/nanopore_svs_cnas/011b-nanopore_sv_gene_stats_nb_model_anova.R"

#     output:
#         nanopore_sv_gene_stats_file = RES_DIR + "/analysis_nanopore/svs/nanopore_sv_gene_stats_R.tsv"
        
#     resources:
#         threads = 20,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '3G'
        
#     shell:
#         "{RSCRIPT} {input.script} --svs_per_gene_file {input.svs_per_gene_file} --nanopore_sv_gene_stats_file {output.nanopore_sv_gene_stats_file} --threads {resources.threads}"



# # merge nanopore and WGS CNAs / SV p-values
# rule merge_nanopore_wgs_cnas_svs_pvalues:
#     input:
#         nanopore_sv_gene_stats_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/nanopore_sv_gene_stats.tsv",
#         cna_continuous_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_gene_continuous_stats.tsv",

#         script = BIN_DIR + "/nanopore_svs_cnas/010b-merge_nanopore_wgs_cnas_svs_pvalues.R"

#     output:
#         nanopore_wgs_cnas_svs_merged_file = RES_DIR + "/analysis_nanopore/svs/nanopore_wgs_cnas_svs_merged.tsv"
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{AP_RSCRIPT} {input.script} --nanopore_sv_gene_stats_file {input.nanopore_sv_gene_stats_file} --cna_continuous_stats_file {input.cna_continuous_stats_file} --nanopore_wgs_cnas_svs_merged_file {output.nanopore_wgs_cnas_svs_merged_file}"




###################################
# recurrently mutated genes SNVs primary-recurrent 
###################################

# process vcf to primary-recurrent contingency table per gene
rule process_vcf_to_contingency_table:
    input:
        combined_vcf_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP_expanded.tsv",
        gene_translation_file = REF_DATA_DIR + "/gene_translations/ensembl_hg38.tsv",

        script = BIN_DIR + "/sarek_wgs/004a-process_vcf_to_contingency_table.py"

    output:
        snv_contingency_file = RES_DIR + "/analysis_sarek/consensus_vcfs/snv_contingency.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_vcf_file {input.combined_vcf_file} --gene_translation_file {input.gene_translation_file} --snv_contingency_file {output.snv_contingency_file}"

rule vcf_contingency_table_stats:
    input:
        snv_contingency_file = RES_DIR + "/analysis_sarek/consensus_vcfs/snv_contingency.tsv",

        script = BIN_DIR + "/sarek_wgs/004b-vcf_contingency_table_stats.py"

    output:
        snv_stats_file = RES_DIR + "/analysis_sarek/consensus_vcfs/vcf_contingency_table_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --snv_contingency_file {input.snv_contingency_file} --snv_stats_file {output.snv_stats_file} --threads {resources.threads}"


# prepare data for stats testing mutation rates in genome
rule accessible_genome_size:
    input:
        genome_blacklist_regions_bed = REF_DATA_DIR + "/genome_size_accessible_blacklist/hg38-blacklist.v2.bed",

        script = BIN_DIR + "/sarek_wgs/005a-accessible_genome_size.py"

    output:
        genome_size_file = REF_DATA_DIR + "/genome_size_accessible_blacklist/genome_size.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --genome_blacklist_regions_bed {input.genome_blacklist_regions_bed} --genome_size_file {output.genome_size_file}"

rule mutation_rates_per_protein_coding_gene:
    input:
        combined_vcf_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP_expanded.tsv",
        pcg_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
        gene_translation_file = REF_DATA_DIR + "/gene_translations/ensembl_hg38.tsv",

        script = BIN_DIR + "/sarek_wgs/005b-mutation_rates_per_protein_coding_gene.py"

    output:
        mutations_per_gene_file = RES_DIR + "/analysis_sarek/gene_impacts/mutation_rates_per_protein_coding_gene.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_vcf_file {input.combined_vcf_file} --pcg_bed_file {input.pcg_bed_file} --gene_translation_file {input.gene_translation_file} --mutations_per_gene_file {output.mutations_per_gene_file}"


# negative binomial model to test mutation rates in genes
rule snv_mutations_negative_binomial_stats:
    input:
        genome_size_file = REF_DATA_DIR + "/genome_size_accessible_blacklist/genome_size.tsv",
        pcg_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        mutations_per_gene_file = RES_DIR + "/analysis_sarek/gene_impacts/mutation_rates_per_protein_coding_gene.tsv",

        script = BIN_DIR + "/sarek_wgs/005c-snv_mutations_negative_binomial_stats.py"

    output:
        snv_negative_binomial_stats_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_negative_binomial_stats.tsv"
        
    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "{PYTHON} {input.script} --genome_size_file {input.genome_size_file} --pcg_bed_file {input.pcg_bed_file} --mutations_per_gene_file {input.mutations_per_gene_file} --snv_negative_binomial_stats_file {output.snv_negative_binomial_stats_file} --threads {resources.threads}"


# combine pvalues using DPM
rule snv_mutations_nb_dpm_prep:
    input:
        snv_negative_binomial_stats_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_negative_binomial_stats.tsv",

        script = BIN_DIR + "/sarek_wgs/005d-snv_mutations_nb_dpm_prep.py"

    output:
        pval_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_mutations_nb_pvals.tsv",
        fc_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_mutations_nb_fcs.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --snv_negative_binomial_stats_file {input.snv_negative_binomial_stats_file} --pval_file {output.pval_file} --fc_file {output.fc_file}"

rule snv_mutations_nb_DPM:
    input:
        pval_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_mutations_nb_pvals.tsv",
        fc_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_mutations_nb_fcs.tsv",
        
        script = BIN_DIR + "/sarek_wgs/005e-snv_mutations_nb_DPM.R"

    output:
        dpm_merged_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_mutations_nb_DPM_pvals.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RDEV_SCRIPT} {input.script} --fc_file {input.fc_file} --pval_file {input.pval_file} --dpm_merged_file {output.dpm_merged_file}"

rule snv_mutations_nb_combine_post_DPM:
    input:
        snv_negative_binomial_stats_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_negative_binomial_stats.tsv",
        dpm_merged_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_mutations_nb_DPM_pvals.tsv",

        script = BIN_DIR + "/sarek_wgs/005f-snv_mutations_nb_combine_post_DPM.py"

    output:
        differential_snv_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_nb_differentially_mutated.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --snv_negative_binomial_stats_file {input.snv_negative_binomial_stats_file} --dpm_merged_file {input.dpm_merged_file} --differential_snv_file {output.differential_snv_file}"






# # combine pcg-cnas into a single file
# rule combine_all_pcg_cnas:
#     input:
#         expand("{res_dir}/analysis_sarek/gene_impacts/{sample}-{tumor}_cnas_pcgs.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

#         script = BIN_DIR + "/sarek_cnas/009a-combine_all_pcg_cnas.py"

#     output:
#         combined_pcg_cna_file = RES_DIR + "/analysis_sarek/gene_impacts/all_combined_pcg_cnas.bed"
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_pcg_cna_file {output.combined_pcg_cna_file}"

# # CNA analysis categorical
# rule cna_gene_primary_recurrent_contingency_prep:
#     input:
#         combined_pcg_cna_file = RES_DIR + "/analysis_sarek/gene_impacts/all_combined_pcg_cnas.bed",

#         script = BIN_DIR + "/sarek_cnas/009b-cna_gene_primary_recurrent_contingency_prep.py"

#     output:
#         cna_contingency_file = RES_DIR + "/analysis_sarek/gene_impacts/cna_gene_primary_recurrent_contingency_prep.tsv"
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --combined_pcg_cna_file {input.combined_pcg_cna_file} --cna_contingency_file {output.cna_contingency_file}"


# rule testing_cna_gene_primary_recurrent_contingency:
#     input:
#         cna_contingency_file = RES_DIR + "/analysis_sarek/gene_impacts/cna_gene_primary_recurrent_contingency_prep.tsv",

#         script = BIN_DIR + "/sarek_cnas/009c-testing_cna_gene_primary_recurrent_contingency.py"

#     output:
#         cna_gene_contingency_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_gene_contingency_file.tsv"
        
#     resources:
#         threads = 5,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '7G'
        
#     shell:
#         "{PYTHON} {input.script} --cna_contingency_file {input.cna_contingency_file} --cna_gene_contingency_stats_file {output.cna_gene_contingency_stats_file} --threads {resources.threads}"

