{"cells": [{"cell_type": "code", "execution_count": null, "id": "d3419a12-ba6e-4c45-9014-8f3d07b85215", "metadata": {}, "outputs": [], "source": ["# load packages\n", "import sys, getopt, time, subprocess, os, pathlib, glob, re\n", "import gzip, io\n", "\n", "try:\n", "    sys.path.insert(1, \"/\".join(os.path.realpath(__file__).split(\"/\")[:-2]))\n", "except:\n", "    sys.path.insert(1, \"/\".join(os.getcwd().split(\"/\")[:-1]))\n", "\n", "\n", "import pandas as pd \n", "import numpy as np\n", "from lifelines import CoxPHFitter, utils\n", "\n", "# from common_utils.a_data_preprocessing import generate_cancer_abbreviation, generate_cancer_tissue_source_dict\n", "import scipy.stats as stats\n", "import statsmodels.stats.multitest as multitest\n"]}, {"cell_type": "code", "execution_count": null, "id": "dac2f83b-8f47-4d3d-80c6-fc815232289c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d13d1175-2bb9-4c48-b313-778687a79c51", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "750f6f34-d334-4bbb-8274-20242d2efa20", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e7f45eb5-57af-48b1-b838-32e989444910", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2fb07fd3-faef-4fd6-9c21-b263f91dc291", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}