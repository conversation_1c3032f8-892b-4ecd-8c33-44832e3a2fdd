# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats

from concurrent.futures import ThreadPoolExecutor

help_message = '''
Failed
'''


# Function to calculate p-value for a gene's number of mutations
def negative_binomial_test(arg):
    gene, tumor, gene_size, genome_size, observed_mutations, total_mutations = arg

    # Calculate the expected number of mutations for the gene based on its size
    expected_mutations = (gene_size / genome_size) * total_mutations
    
    # Estimate the parameters of the negative binomial distribution
    # Using the mean (expected_mutations) and variance
    # Neg Binomial: mean = mu, variance = mu + mu^2 / r
    # For simplicity, we'll assume that the shape parameter (r) is related to the total mutations' overdispersion
    mu = expected_mutations
    r = mu  # Shape parameter (often estimated or assumed to be similar to the mean in many cases)

    
    # Negative binomial p-value: P(X >= observed_mutations)
    p_value = 1 - stats.nbinom.cdf(observed_mutations, r, mu / (mu + r))
    
    return [gene, tumor, p_value, expected_mutations, observed_mutations, np.log2((observed_mutations+1) / (expected_mutations+1))]



def run_negative_binomial_stats_genes(genome_size_file, pcg_bed_file, mutations_per_gene_file, threads):
    # Optimized file reading
    genome_size = pd.read_csv(genome_size_file, sep="\t")
    pcg_df = pd.read_csv(pcg_bed_file, sep="\t", header=None, names=['chr', 'start', 'stop', 'gene', 'gene_type', 'information'])
    mutations_df = pd.read_csv(mutations_per_gene_file, sep="\t")

    # Preprocess data
    pcg_df = pcg_df.drop_duplicates(subset='gene')
    pcg_df['size'] = pcg_df['stop'] - pcg_df['start']

    # create dict
    gene_sizes = pcg_df.set_index('gene')['size'].to_dict()

    # get genome size
    genome_size = genome_size.loc[0, 'accessible_genome_size']


    # total mutations from each set
    mutations_df.index = mutations_df['gene_tumor']
    total_mutations_primary = mutations_df.loc['total_mutations_primary', 'mutations']
    total_mutations_recurrent = mutations_df.loc['total_mutations_recurrent', 'mutations']
    
    # Filter mutations and create dictionaries for fast lookup
    genes_of_interest = mutations_df['gene'].dropna().unique()

    primary_df = mutations_df[mutations_df['tumor'] == 'primary']
    recurrent_df = mutations_df[mutations_df['tumor'] == 'recurrent']

    # create dict for faster lookup
    mutations_primary = primary_df.set_index('gene')['mutations'].to_dict()
    mutations_recurrent = recurrent_df.set_index('gene')['mutations'].to_dict()

    # Combine arguments for both primary and recurrent
    all_args = [
        (gene, tumor, gene_sizes.get(gene, None), genome_size, mutations.get(gene, 0), total_mutations)
        for gene in genes_of_interest
        for tumor, mutations, total_mutations in [
            ('primary', mutations_primary, total_mutations_primary),
            ('recurrent', mutations_recurrent, total_mutations_recurrent)
        ]
    ]

    # Parallelize processing
    with ThreadPoolExecutor(max_workers=threads) as executor:
        results = list(executor.map(negative_binomial_test, all_args))

    # Convert results to a DataFrame
    df = pd.DataFrame(
        results,
        columns=['gene', 'tumor', 'p_value', 'expected_mutations', 'observed_mutations', 'foldchange']
    )

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data and process into contingency table
    df = run_negative_binomial_stats_genes(genome_size_file, pcg_bed_file, mutations_per_gene_file, threads)

    # save the contingency table
    df.to_csv(snv_negative_binomial_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["genome_size_file=", "pcg_bed_file=", "mutations_per_gene_file=", "snv_negative_binomial_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--genome_size_file"):
            genome_size_file = str(arg)
        if opt in ("--pcg_bed_file"):
            pcg_bed_file = str(arg)
        if opt in ("--mutations_per_gene_file"):
            mutations_per_gene_file = str(arg)

        if opt in ("--snv_negative_binomial_stats_file"):
            snv_negative_binomial_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()



