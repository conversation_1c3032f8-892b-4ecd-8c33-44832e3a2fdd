# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def generate_differentailly_mutated_df(snv_negative_binomial_stats_file, dpm_merged_file):
    # load dfs
    snv_df = pd.read_csv(snv_negative_binomial_stats_file, sep="\t")
    dpm_merged = pd.read_csv(dpm_merged_file, sep="\t")

    # set index to gene
    snv_df.index = snv_df['gene']
    dpm_merged.index = dpm_merged['gene']

    # calculate differential methylation between recurrent-primary
    primary_df = snv_df[snv_df['tumor'] == 'primary']
    recurrent_df = snv_df[snv_df['tumor'] == 'recurrent']

    # calculate fold change
    res_df = recurrent_df[['observed_mutations']]

    res_df['observed_mutations'] = np.log2(res_df['observed_mutations'] / primary_df['observed_mutations'])

    # add p-value from dpm_merged to res_df
    res_df = pd.concat([res_df, dpm_merged['dpm_pvalues']], axis=1)

    # rename columns
    res_df.columns = ['foldchange', 'pvalue']
    res_df['gene'] = res_df.index.to_numpy()
    
    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    res_df = generate_differentailly_mutated_df(snv_negative_binomial_stats_file, dpm_merged_file)

    # save to files
    res_df.to_csv(differential_snv_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["snv_negative_binomial_stats_file=", "dpm_merged_file=", "differential_snv_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--snv_negative_binomial_stats_file"):
            snv_negative_binomial_stats_file = str(arg)
        if opt in ("--dpm_merged_file"):
            dpm_merged_file = str(arg)

        if opt in ("--differential_snv_file"):
            differential_snv_file = str(arg)


    main()



