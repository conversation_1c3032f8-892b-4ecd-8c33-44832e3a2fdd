# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ThreadPoolExecutor
import scipy.stats as stats

help_message = '''
Failed
'''

def run_stats_on_row(row, n_samples_per_condition=12):
    # compare any gain / loss to normal
    primary_mutated = row['primary']
    recurrent_mutated = row['recurrent']
    primary_not_mutated = n_samples_per_condition - primary_mutated
    recurrent_not_mutated = n_samples_per_condition - recurrent_mutated

    contingency = np.array([[primary_mutated, primary_not_mutated], [recurrent_mutated, recurrent_not_mutated]])

    p_val = stats.fisher_exact(contingency)[1]
    fc = np.log2((recurrent_mutated+1) / (primary_mutated+1))

    return [row['gene'], p_val, fc]


def run_stats_on_contingency_table(snv_contingency_file, threads):
    # load df
    df = pd.read_csv(snv_contingency_file, sep="\t", index_col=0)

    # add gene columns
    df['gene'] = df.index.to_numpy()

    # create arguments for multi-threading
    args = [(row) for i, row in df.iterrows()]

    # run stats on each row
    with ThreadPoolExecutor(max_workers=threads) as executor:
        results = executor.map(run_stats_on_row, args)

    # create a new df with p-values
    pvalues = pd.DataFrame(results, columns=['gene', 'pvalue', 'foldchange'])

    return pvalues


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = run_stats_on_contingency_table(snv_contingency_file, threads)

    # save stats table
    df.to_csv(snv_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["snv_contingency_file=", "snv_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--snv_contingency_file"):
            snv_contingency_file = str(arg)
            
        if opt in ("--snv_stats_file"):
            snv_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()



