# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

import pandas as pd 
import numpy as np

from concurrent.futures import ThreadPoolExecutor
import scipy.stats as stats

help_message = '''
Failed
'''

def run_stats_on_row(row):
    # separate into primary and recurrent
    primary_array = row[row.index.str.contains('primary')].to_numpy(dtype='float')
    recurrent_array = row[row.index.str.contains('recurrent')].to_numpy(dtype='float')

    # calculate the log2 ratio of the number of values in recurrent over primary that are not equal to 2
    fc = np.log2(np.sum(recurrent_array != 2) / np.sum(primary_array != 2))

    # if the arrays are the same, the p-value is 1
    if np.all(primary_array == recurrent_array):
        ttest_pvalue = 1
        utest_pvalue = 1
    else:
        # run paired t-test and paired u-test test
        ttest_pvalue = stats.ttest_rel(primary_array, recurrent_array).pvalue
        utest_pvalue = stats.wilcoxon(primary_array, recurrent_array).pvalue

    return [row['gene'], ttest_pvalue, utest_pvalue, fc]


def run_stats_on_contingency_table(cna_continuous_file, threads):
    # load df
    df = pd.read_csv(cna_continuous_file, sep="\t", index_col=0)

    # add gene columns
    df['gene'] = df.index.to_numpy()

    # create arguments for multi-threading
    args = [(row) for i, row in df.iterrows()]

    # run stats on each row
    with ThreadPoolExecutor(max_workers=threads) as executor:
        results = executor.map(run_stats_on_row, args)

    # create a new df with p-values
    pvalues = pd.DataFrame(results, columns=['gene', 'ttest_pvalue', 'utest_pvalue', 'foldchange'])

    return pvalues


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = run_stats_on_contingency_table(cna_continuous_file, threads)

    # save stats table
    df.to_csv(cna_continuous_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_continuous_file=", "cna_continuous_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--cna_continuous_file"):
            cna_continuous_file = str(arg)
            
        if opt in ("--cna_continuous_stats_file"):
            cna_continuous_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()



