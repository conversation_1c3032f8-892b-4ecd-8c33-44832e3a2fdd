# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ThreadPoolExecutor
import scipy.stats as stats

help_message = '''
Failed
'''

def calculate_accessible_genome_size(genome_blacklist_regions_bed, hg38_size = 3088269832):
    # load the blacklist regions
    df = pd.read_csv(genome_blacklist_regions_bed, sep="\t", header=None)
    df.columns = ['chr', 'start', 'end', 'reason']

    # calculate the size of the blacklist regions
    blacklist_size = df['end'] - df['start']
    blacklist_size = blacklist_size.sum()

    # subtract from the total genome size
    accessible_genome_size = hg38_size - blacklist_size

    # create df for hg38
    df = pd.DataFrame({'genome': ['hg38'], 'size': [hg38_size], 'accessible_genome_size': [accessible_genome_size], 'blacklist_size': [blacklist_size]})

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = calculate_accessible_genome_size(genome_blacklist_regions_bed)

    # save stats table
    df.to_csv(genome_size_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["genome_blacklist_regions_bed=", "genome_size_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--genome_blacklist_regions_bed"):
            genome_blacklist_regions_bed = str(arg)
            
        if opt in ("--genome_size_file"):
            genome_size_file = str(arg)


    main()



