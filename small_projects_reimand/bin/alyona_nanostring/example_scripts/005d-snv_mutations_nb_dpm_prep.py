# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def prepare_for_dpm(snv_negative_binomial_stats_file):
    # load df
    df = pd.read_csv(snv_negative_binomial_stats_file, sep="\t")

    # set index as gene
    df.index = df['gene']


    # subset to primary and recurrent tumors for p_value and fold change
    primary_pval_df = df.loc[df['tumor'] == 'primary', ['p_value']]
    recurrent_pval_df = df.loc[df['tumor'] == 'recurrent', ['p_value']]

    primary_fc_df = df.loc[df['tumor'] == 'primary', ['foldchange']]
    recurrent_fc_df = df.loc[df['tumor'] == 'recurrent', ['foldchange']]

    # rename columns to primary and recurrent
    primary_pval_df.columns = ['primary']
    recurrent_pval_df.columns = ['recurrent']

    primary_fc_df.columns = ['primary']
    recurrent_fc_df.columns = ['recurrent']

    # combine dfs
    pval_df = pd.concat([primary_pval_df, recurrent_pval_df], axis=1)
    fc_df = pd.concat([primary_fc_df, recurrent_fc_df], axis=1)

    return pval_df, fc_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    pval_df, fc_df = prepare_for_dpm(snv_negative_binomial_stats_file)

    # save stats table
    pval_df.to_csv(pval_file, sep="\t")
    fc_df.to_csv(fc_file, sep="\t")

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["snv_negative_binomial_stats_file=", "pval_file=", "fc_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--snv_negative_binomial_stats_file"):
            snv_negative_binomial_stats_file = str(arg)

        if opt in ("--pval_file"):
            pval_file = str(arg)
        if opt in ("--fc_file"):
            fc_file = str(arg)


    main()



