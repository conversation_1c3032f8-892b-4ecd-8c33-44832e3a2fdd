# <PERSON>
library(optparse)
library(ActivePathways)

# options list for parser options
option_list <- list(
    make_option(c("-a","--pval_file"), type="character", default=NULL,
            help="",
            dest="pval_file"),
    make_option(c("-b","--fc_file"), type="character", default=NULL,
            help="",
            dest="fc_file"),
    make_option(c("-c","--dpm_merged_file"), type="character", default=NULL,
            help="",
            dest="dpm_merged_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# load p-value df
pval_df = read.csv(opt$pval_file, sep='\t', row.names=1)
scores = as.matrix(pval_df)
rownames(scores) = rownames(pval_df)

# load fold change df
fc_df = read.csv(opt$fc_file, sep='\t', row.names=1)
scores_dir = as.matrix(fc_df)
rownames(scores_dir) = rownames(fc_df)

# merge using DPM
dpm_pvalues = merge_p_values(scores, scores_direction = scores_dir, constraints_vector = c(-1, 1), method='DPM')

# create dfs for the p-values
dpm_pvalues = data.frame(dpm_pvalues)
dpm_pvalues$gene = rownames(dpm_pvalues)


# write the merged p-values to a file
write.table(dpm_pvalues, file=opt$dpm_merged_file, sep='\t', row.names=FALSE, quote=FALSE)

print("P-value merging Complete")




