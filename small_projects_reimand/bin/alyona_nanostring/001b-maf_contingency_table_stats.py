# Alec <PERSON>i - statistical testing for differential mutation analysis

import sys, getopt, time, os
import pandas as pd 
import numpy as np
from concurrent.futures import ThreadPoolExecutor
import scipy.stats as stats

help_message = '''
Perform statistical testing on contingency table for differential mutation analysis
Usage: python 001b-maf_contingency_table_stats.py --contingency_file <file> --stats_file <output> --threads <int>
'''

def run_stats_on_row(row):
    """
    Run Fisher's exact test on a single gene's contingency table
    """
    gene = row['gene']
    early_mutated = int(row['early_mutated'])
    early_not_mutated = int(row['early_not_mutated'])
    late_mutated = int(row['late_mutated'])
    late_not_mutated = int(row['late_not_mutated'])
    
    # Create 2x2 contingency table
    # Rows: early vs late
    # Columns: mutated vs not mutated
    contingency = np.array([
        [early_mutated, early_not_mutated],
        [late_mutated, late_not_mutated]
    ])
    
    # Perform Fisher's exact test
    try:
        odds_ratio, p_val = stats.fisher_exact(contingency)
        
        # Calculate fold change (log2)
        # Add pseudocount to avoid division by zero
        early_rate = (early_mutated + 1) / (early_mutated + early_not_mutated + 2)
        late_rate = (late_mutated + 1) / (late_mutated + late_not_mutated + 2)
        fold_change = np.log2(late_rate / early_rate)
        
        # Calculate effect direction
        if late_mutated > early_mutated:
            direction = "late_enriched"
        elif late_mutated < early_mutated:
            direction = "early_enriched"
        else:
            direction = "no_difference"
            
    except Exception as e:
        print(f"Error processing gene {gene}: {e}")
        p_val = 1.0
        fold_change = 0.0
        odds_ratio = 1.0
        direction = "error"
    
    return [gene, p_val, fold_change, odds_ratio, direction, 
            early_mutated, early_not_mutated, late_mutated, late_not_mutated]

def run_stats_on_contingency_table(contingency_file, threads):
    """
    Load contingency table and run statistical tests
    """
    print(f"Loading contingency table from: {contingency_file}")
    
    # Load contingency table
    df = pd.read_csv(contingency_file, sep="\t")
    
    print(f"Loaded {len(df)} genes for statistical testing")
    
    # Filter out genes with no mutations in either group (optional)
    df_filtered = df[(df['early_mutated'] > 0) | (df['late_mutated'] > 0)]
    print(f"Testing {len(df_filtered)} genes with at least one mutation")
    
    # Create arguments for multi-threading
    args = [row for _, row in df_filtered.iterrows()]
    
    print(f"Running statistical tests using {threads} threads...")
    
    # Run stats on each row
    with ThreadPoolExecutor(max_workers=threads) as executor:
        results = list(executor.map(run_stats_on_row, args))
    
    # Create results dataframe
    results_df = pd.DataFrame(results, columns=[
        'gene', 'pvalue', 'log2_fold_change', 'odds_ratio', 'direction',
        'early_mutated', 'early_not_mutated', 'late_mutated', 'late_not_mutated'
    ])
    
    # Sort by p-value
    results_df = results_df.sort_values('pvalue')
    
    # Add multiple testing correction (Benjamini-Hochberg)
    from statsmodels.stats.multitest import multipletests
    
    _, pvals_corrected, _, _ = multipletests(results_df['pvalue'], method='fdr_bh')
    results_df['pvalue_adjusted'] = pvals_corrected
    
    print(f"Statistical testing completed for {len(results_df)} genes")
    
    # Print summary statistics
    significant_genes = results_df[results_df['pvalue_adjusted'] < 0.05]
    print(f"Significant genes (FDR < 0.05): {len(significant_genes)}")
    
    if len(significant_genes) > 0:
        late_enriched = significant_genes[significant_genes['direction'] == 'late_enriched']
        early_enriched = significant_genes[significant_genes['direction'] == 'early_enriched']
        print(f"  - Late-enriched: {len(late_enriched)}")
        print(f"  - Early-enriched: {len(early_enriched)}")
    
    return results_df

def main():
    print('001b-maf_contingency_table_stats.py')
    t1 = time.time()

    # Run statistical testing
    results_df = run_stats_on_contingency_table(contingency_file, threads)

    # Save results
    results_df.to_csv(stats_file, sep="\t", index=False)
    
    print(f"Saved statistical results to: {stats_file}")
    print(f"Processing completed in {round(time.time() - t1, 2)} seconds")
    print('001b-maf_contingency_table_stats.py COMPLETE')

if __name__ == "__main__":
    threads = 1
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["contingency_file=", "stats_file=", "threads="])
    except getopt.GetoptError:
        print(help_message)
        print(sys.argv)
        sys.exit(2)
    
    for opt, arg in opts:
        if opt in ("--contingency_file"):
            contingency_file = str(arg)
        elif opt in ("--stats_file"):
            stats_file = str(arg)
        elif opt in ("--threads"):
            threads = int(arg)
    
    main()
